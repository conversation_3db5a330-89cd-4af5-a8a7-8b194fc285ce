// test/purchase_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:brainy_bunny/services/purchase_service.dart';
import 'package:brainy_bunny/constants/revenue_cat_constants.dart';

void main() {
  group('PurchaseService Tests', () {
    test('PurchaseService singleton instance', () {
      final instance1 = PurchaseService.instance;
      final instance2 = PurchaseService.instance;
      
      expect(instance1, equals(instance2));
    });

    test('PurchaseService initial state', () {
      final service = PurchaseService.instance;
      
      // Initially should not be initialized
      expect(service.isInitialized, isFalse);
      expect(service.isPurchased, isFalse);
      expect(service.isFullGamePurchased, isFalse);
    });

    test('PurchaseService streams', () {
      final service = PurchaseService.instance;
      
      // Test streams are available
      expect(service.purchaseStream, isA<Stream<bool>>());
      expect(service.priceStream, isA<Stream<String>>());
    });

    test('PurchaseService callback setup', () {
      final service = PurchaseService.instance;
      bool successCalled = false;
      bool errorCalled = false;
      bool pendingCalled = false;
      bool invalidCalled = false;
      bool pendingRemoveCalled = false;
      
      // Test callback assignment
      service.onPurchaseSuccess = () => successCalled = true;
      service.onPurchaseError = () => errorCalled = true;
      service.onPurchasePending = () => pendingCalled = true;
      service.onPurchaseInvalid = () => invalidCalled = true;
      service.onPurchasePendingRemove = () => pendingRemoveCalled = true;
      
      expect(service.onPurchaseSuccess, isNotNull);
      expect(service.onPurchaseError, isNotNull);
      expect(service.onPurchasePending, isNotNull);
      expect(service.onPurchaseInvalid, isNotNull);
      expect(service.onPurchasePendingRemove, isNotNull);
      
      // Manually trigger callbacks to test
      service.onPurchaseSuccess?.call();
      service.onPurchaseError?.call();
      service.onPurchasePending?.call();
      service.onPurchaseInvalid?.call();
      service.onPurchasePendingRemove?.call();
      
      expect(successCalled, isTrue);
      expect(errorCalled, isTrue);
      expect(pendingCalled, isTrue);
      expect(invalidCalled, isTrue);
      expect(pendingRemoveCalled, isTrue);
    });

    test('PurchaseService price handling', () {
      final service = PurchaseService.instance;
      
      // Should return some price when not initialized
      expect(service.displayPrice, isNotEmpty);
      expect(service.isUsingFallbackPrice, isA<bool>());
      expect(service.priceLoaded, isA<bool>());
      expect(service.priceLoadError, isA<String?>());
    });

    test('PurchaseService service status', () {
      final service = PurchaseService.instance;
      final status = service.getServiceStatus();
      
      expect(status, isA<Map<String, dynamic>>());
      expect(status.containsKey('isInitialized'), isTrue);
      expect(status.containsKey('useRevenueCat'), isTrue);
      expect(status.containsKey('revenueCatConfigured'), isTrue);
      expect(status.containsKey('revenueCatInitialized'), isTrue);
      expect(status.containsKey('legacyInitialized'), isTrue);
      expect(status.containsKey('isPurchased'), isTrue);
      expect(status.containsKey('displayPrice'), isTrue);
      expect(status.containsKey('priceLoaded'), isTrue);
      
      // Verify status values are correct types
      expect(status['isInitialized'], isA<bool>());
      expect(status['useRevenueCat'], isA<bool>());
      expect(status['revenueCatConfigured'], isA<bool>());
      expect(status['revenueCatInitialized'], isA<bool>());
      expect(status['legacyInitialized'], isA<bool>());
      expect(status['isPurchased'], isA<bool>());
      expect(status['displayPrice'], isA<String>());
      expect(status['priceLoaded'], isA<bool>());
    });

    test('PurchaseService purchase methods exist', () {
      final service = PurchaseService.instance;
      
      // These should not throw exceptions even if not initialized
      expect(() => service.purchaseFullGame(), returnsNormally);
      expect(() => service.restorePurchases(), returnsNormally);
    });

    test('PurchaseService configuration awareness', () {
      final service = PurchaseService.instance;
      final status = service.getServiceStatus();
      
      // Should be aware of RevenueCat configuration status
      expect(status['revenueCatConfigured'], equals(RevenueCatConstants.isConfigured));
    });
  });

  group('PurchaseService Integration Tests', () {
    test('Service selection logic', () {
      final service = PurchaseService.instance;
      final status = service.getServiceStatus();
      
      // If RevenueCat is configured, it should be preferred
      if (RevenueCatConstants.isConfigured) {
        // RevenueCat should be attempted as primary service
        expect(status['revenueCatConfigured'], isTrue);
      } else {
        // Should fall back to legacy service
        expect(status['revenueCatConfigured'], isFalse);
      }
    });

    test('Fallback mechanism', () {
      final service = PurchaseService.instance;
      
      // Service should always have a working purchase mechanism
      expect(service.displayPrice, isNotEmpty);
      expect(() => service.purchaseFullGame(), returnsNormally);
      expect(() => service.restorePurchases(), returnsNormally);
    });

    test('State consistency', () {
      final service = PurchaseService.instance;
      
      // Purchase state should be consistent
      expect(service.isPurchased, equals(service.isFullGamePurchased));
      
      // Price state should be consistent
      if (service.priceLoaded) {
        expect(service.displayPrice, isNotEmpty);
      }
    });
  });
}

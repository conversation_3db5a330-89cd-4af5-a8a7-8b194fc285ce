// test/revenue_cat_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:brainy_bunny/services/revenue_cat_service.dart';
import 'package:brainy_bunny/constants/revenue_cat_constants.dart';

void main() {
  group('RevenueCatService Tests', () {
    test('RevenueCatService singleton instance', () {
      final instance1 = RevenueCatService.instance;
      final instance2 = RevenueCatService.instance;
      
      expect(instance1, equals(instance2));
    });

    test('RevenueCatService initial state', () {
      final service = RevenueCatService.instance;
      
      // Initially should not be initialized
      expect(service.isInitialized, isFalse);
      expect(service.isPurchased, isFalse);
      expect(service.isFullGamePurchased, isFalse);
    });

    test('RevenueCatService configuration validation', () {
      // Test configuration status
      expect(RevenueCatConstants.isConfigured, isA<bool>());
      expect(RevenueCatConstants.configurationStatus, isA<String>());
      
      // Test API key getter
      expect(() => RevenueCatConstants.API_KEY, returnsNormally);
      
      // Test product ID getter
      expect(RevenueCatConstants.FULL_GAME_PRODUCT_ID, isNotEmpty);
    });

    test('RevenueCatService price handling', () {
      final service = RevenueCatService.instance;
      
      // Should return fallback price when not initialized
      expect(service.displayPrice, isNotEmpty);
      expect(service.isUsingFallbackPrice, isTrue);
      expect(service.priceLoaded, isFalse);
    });

    test('RevenueCatService callback setup', () {
      final service = RevenueCatService.instance;
      bool callbackCalled = false;
      
      // Test callback assignment
      service.onPurchaseSuccess = () {
        callbackCalled = true;
      };
      
      expect(service.onPurchaseSuccess, isNotNull);
      
      // Manually trigger callback to test
      service.onPurchaseSuccess?.call();
      expect(callbackCalled, isTrue);
    });

    test('RevenueCatService stream controllers', () {
      final service = RevenueCatService.instance;
      
      // Test streams are available
      expect(service.purchaseStream, isA<Stream<bool>>());
      expect(service.customerInfoStream, isA<Stream>());
      expect(service.offeringsStream, isA<Stream>());
    });

    test('RevenueCatService entitlement checking', () {
      final service = RevenueCatService.instance;
      
      // Should handle null customer info gracefully
      expect(service.hasEntitlement('test_entitlement'), isFalse);
      expect(service.customerInfo, isNull);
      expect(service.offerings, isNull);
      expect(service.currentPackage, isNull);
    });
  });

  group('RevenueCatConstants Tests', () {
    test('Product ID platform selection', () {
      final productId = RevenueCatConstants.FULL_GAME_PRODUCT_ID;
      expect(productId, isNotEmpty);
      expect(productId, anyOf([
        RevenueCatConstants.IOS_FULL_GAME_PRODUCT,
        RevenueCatConstants.ANDROID_FULL_GAME_PRODUCT,
      ]));
    });

    test('API key platform selection', () {
      expect(() => RevenueCatConstants.API_KEY, returnsNormally);
    });

    test('Entitlement constants', () {
      expect(RevenueCatConstants.FULL_GAME_ENTITLEMENT, isNotEmpty);
      expect(RevenueCatConstants.PREMIUM_ENTITLEMENT, isNotEmpty);
    });

    test('Offering constants', () {
      expect(RevenueCatConstants.DEFAULT_OFFERING, isNotEmpty);
      expect(RevenueCatConstants.FULL_GAME_OFFERING, isNotEmpty);
    });

    test('Package constants', () {
      expect(RevenueCatConstants.FULL_GAME_PACKAGE, isNotEmpty);
      expect(RevenueCatConstants.MONTHLY_PACKAGE, isNotEmpty);
      expect(RevenueCatConstants.ANNUAL_PACKAGE, isNotEmpty);
      expect(RevenueCatConstants.LIFETIME_PACKAGE, isNotEmpty);
    });

    test('User attribute constants', () {
      expect(RevenueCatConstants.USER_ATTRIBUTE_GAMES_PLAYED, isNotEmpty);
      expect(RevenueCatConstants.USER_ATTRIBUTE_LAST_GAME_DATE, isNotEmpty);
      expect(RevenueCatConstants.USER_ATTRIBUTE_FAVORITE_GAME, isNotEmpty);
      expect(RevenueCatConstants.USER_ATTRIBUTE_TOTAL_SCORE, isNotEmpty);
    });

    test('Dashboard URL constants', () {
      expect(RevenueCatConstants.DASHBOARD_URL, startsWith('https://'));
      expect(RevenueCatConstants.ENTITLEMENTS_URL, startsWith('https://'));
      expect(RevenueCatConstants.PRODUCTS_URL, startsWith('https://'));
      expect(RevenueCatConstants.OFFERINGS_URL, startsWith('https://'));
    });
  });

  group('RevenueCatSetupInstructions Tests', () {
    test('Setup guide availability', () {
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, isNotEmpty);
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, contains('RevenueCat'));
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, contains('API Keys'));
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, contains('Products'));
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, contains('Entitlements'));
      expect(RevenueCatSetupInstructions.SETUP_GUIDE, contains('Offerings'));
    });
  });
}

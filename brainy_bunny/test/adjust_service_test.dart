// test/adjust_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:brainy_bunny/services/adjust_service.dart';

void main() {
  group('AdjustService Tests', () {
    test('AdjustService singleton instance', () {
      final instance1 = AdjustService.instance;
      final instance2 = AdjustService.instance;
      
      expect(instance1, equals(instance2));
      expect(instance1.appToken, equals('2k0f6660xxxc'));
    });

    test('AdjustService initialization state', () {
      final adjustService = AdjustService.instance;
      
      // Initially should not be initialized
      expect(adjustService.isInitialized, isFalse);
      
      // App token should be correct
      expect(adjustService.appToken, equals('2k0f6660xxxc'));
    });

    test('Event tracking methods exist and handle null parameters', () {
      final adjustService = AdjustService.instance;
      
      // These should not throw exceptions even if not initialized
      expect(() => adjustService.trackAppLaunch(), returnsNormally);
      expect(() => adjustService.trackGameStart('test_game'), returnsNormally);
      expect(() => adjustService.trackGameComplete('test_game', 100, 60), returnsNormally);
      expect(() => adjustService.trackPurchase('test_product', 9.99, 'USD', 'test_txn'), returnsNormally);
    });

    test('Custom event tracking with parameters', () {
      final adjustService = AdjustService.instance;
      
      // Should handle various parameter combinations
      expect(() => adjustService.trackEvent('test_event'), returnsNormally);
      expect(() => adjustService.trackEvent(
        'test_event',
        callbackParameters: {'key1': 'value1'},
      ), returnsNormally);
      expect(() => adjustService.trackEvent(
        'test_event',
        partnerParameters: {'partner_key': 'partner_value'},
        revenue: 5.99,
        currency: 'EUR',
        transactionId: 'test_transaction',
      ), returnsNormally);
    });
  });
}

// lib/ui/overlays/revenuecat_paywall.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/services/revenue_cat_service.dart';
import 'package:brainy_bunny/services/purchase_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// RevenueCat-powered paywall overlay
/// Shows when user tries to access locked content after trial period
class RevenueCatPaywall extends StatefulWidget {
  final VoidCallback? onPurchaseSuccess;
  final VoidCallback? onPurchaseError;
  final VoidCallback? onClose;
  final String? triggeredFrom; // 'locked_level', 'trial_complete', 'manual'

  const RevenueCatPaywall({
    super.key,
    this.onPurchaseSuccess,
    this.onPurchaseError,
    this.onClose,
    this.triggeredFrom,
  });

  @override
  State<RevenueCatPaywall> createState() => _RevenueCatPaywallState();
}

class _RevenueCatPaywallState extends State<RevenueCatPaywall>
    with SingleTickerProviderStateMixin {

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  Offering? _currentOffering;
  Package? _selectedPackage;
  bool _isLoading = true;
  bool _isPurchasing = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadOfferings();
    _trackPaywallView();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  Future<void> _loadOfferings() async {
    try {
      final offerings = await Purchases.getOfferings();

      if (offerings.current != null) {
        setState(() {
          _currentOffering = offerings.current;
          // Select the first available package (usually the main product)
          _selectedPackage = _currentOffering!.availablePackages.isNotEmpty
              ? _currentOffering!.availablePackages.first
              : null;
          _isLoading = false;
        });

        if (kDebugMode) {
          print('🎯 RevenueCat Paywall: Loaded offering with ${_currentOffering!.availablePackages.length} packages');
        }
      } else {
        setState(() {
          _errorMessage = 'No offerings available';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load products: $e';
        _isLoading = false;
      });

      if (kDebugMode) {
        print('❌ RevenueCat Paywall: Failed to load offerings: $e');
      }
    }
  }

  void _trackPaywallView() {
    // Track paywall view in Adjust
    AdjustService.instance.trackEvent('paywall_viewed',
      callbackParameters: {
        'trigger': widget.triggeredFrom ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    if (kDebugMode) {
      print('📊 Paywall viewed - trigger: ${widget.triggeredFrom}');
    }
  }

  Future<void> _purchasePackage(Package package) async {
    if (_isPurchasing) return;

    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      if (kDebugMode) {
        print('🛒 Starting purchase for package: ${package.identifier}');
      }

      // Track purchase attempt
      AdjustService.instance.trackEvent('purchase_attempt',
        callbackParameters: {
          'package_id': package.identifier,
          'price': package.storeProduct.priceString,
          'trigger': widget.triggeredFrom ?? 'unknown',
        },
      );

      final purchaseResult = await Purchases.purchasePackage(package);

      if (purchaseResult.customerInfo.entitlements.active.isNotEmpty) {
        // Purchase successful
        if (kDebugMode) {
          print('✅ Purchase successful!');
        }

        // Track successful purchase
        AdjustService.instance.trackEvent('purchase_success',
          callbackParameters: {
            'package_id': package.identifier,
            'price': package.storeProduct.priceString,
            'currency': package.storeProduct.currencyCode,
          },
          revenue: package.storeProduct.price,
          currency: package.storeProduct.currencyCode,
        );

        // Close paywall and notify success
        widget.onPurchaseSuccess?.call();
        _closePaywall();

      } else {
        throw Exception('Purchase completed but no active entitlements found');
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'Purchase failed: ${e.toString()}';
        _isPurchasing = false;
      });

      // Track purchase failure
      AdjustService.instance.trackEvent('purchase_failed',
        callbackParameters: {
          'package_id': package.identifier,
          'error': e.toString(),
          'trigger': widget.triggeredFrom ?? 'unknown',
        },
      );

      widget.onPurchaseError?.call();

      if (kDebugMode) {
        print('❌ Purchase failed: $e');
      }
    }
  }

  Future<void> _restorePurchases() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      final customerInfo = await Purchases.restorePurchases();

      if (customerInfo.entitlements.active.isNotEmpty) {
        // Restore successful
        if (kDebugMode) {
          print('✅ Purchases restored successfully!');
        }

        AdjustService.instance.trackEvent('purchase_restored');

        widget.onPurchaseSuccess?.call();
        _closePaywall();
      } else {
        setState(() {
          _errorMessage = 'No previous purchases found';
          _isPurchasing = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Restore failed: ${e.toString()}';
        _isPurchasing = false;
      });

      if (kDebugMode) {
        print('❌ Restore failed: $e');
      }
    }
  }

  void _closePaywall() {
    _animationController.reverse().then((_) {
      widget.onClose?.call();
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.8),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    maxHeight: MediaQuery.of(context).size.height * 0.8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: _buildPaywallContent(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPaywallContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_currentOffering == null || _selectedPackage == null) {
      return _buildNoProductsState();
    }

    return _buildMainPaywall();
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
          const SizedBox(height: 20),
          Text(
            'Loading premium content...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: const EdgeInsets.all(30),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 60,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'Oops! Something went wrong',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            _errorMessage ?? 'Unknown error',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 30),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: _closePaywall,
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: _loadOfferings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNoProductsState() {
    return Container(
      padding: const EdgeInsets.all(30),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 60,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'No products available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Premium content is not available right now. Please try again later.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            onPressed: _closePaywall,
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildMainPaywall() {
    final package = _selectedPackage!;
    final product = package.storeProduct;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Header with close button
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade600,
                  Colors.purple.shade600,
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '🎮 Unlock All Games!',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Get access to ${AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT} premium games',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _closePaywall,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(30),
            child: Column(
              children: [
                // Features list
                _buildFeaturesList(),

                const SizedBox(height: 30),

                // Price and purchase button
                _buildPurchaseSection(package, product),

                const SizedBox(height: 20),

                // Restore purchases button
                TextButton(
                  onPressed: _isPurchasing ? null : _restorePurchases,
                  child: Text(
                    'Restore Purchases',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ),

                // Error message
                if (_errorMessage != null) ...[
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    final features = [
      '🎯 ${AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT} Premium Games',
      '🧠 Advanced Learning Activities',
      '🎨 Creative Art & Drawing Games',
      '🔢 Math & Logic Puzzles',
      '🎵 Music & Sound Games',
      '🌟 No Ads, Pure Fun!',
    ];

    return Column(
      children: features.map((feature) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.green.shade500,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                feature,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildPurchaseSection(Package package, StoreProduct product) {
    return Column(
      children: [
        // Price display
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.green.shade50,
                Colors.blue.shade50,
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.blue.shade200,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Text(
                'One-time purchase',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                product.priceString,
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
              Text(
                'Unlock everything forever!',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Purchase button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: _isPurchasing ? null : () => _purchasePackage(package),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 4,
            ),
            child: _isPurchasing
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Processing...',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )
                : Text(
                    'Unlock All Games - ${product.priceString}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
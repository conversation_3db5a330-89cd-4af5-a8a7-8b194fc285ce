// lib/ui/widgets/purchase_button.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import 'package:brainy_bunny/services/purchase_service.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Purchase button with Parent Gate protection and real price display
class PurchaseButton extends StatefulWidget {
  final PurchaseService purchaseService;
  final Color? backgroundColor;
  final Color? textColor;
  final double borderRadius;
  final bool showRestoreButton;

  const PurchaseButton({
    super.key,
    required this.purchaseService,
    this.backgroundColor,
    this.textColor,
    this.borderRadius = 12.0,
    this.showRestoreButton = true,
  });

  @override
  State<PurchaseButton> createState() => _PurchaseButtonState();
}

class _PurchaseButtonState extends State<PurchaseButton>
    with SingleTickerProviderStateMixin {

  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  bool _isProcessing = false;

  // ADDED: Price state management
  String _displayPrice = AppConstants.FALLBACK_PRICE;
  bool _priceLoading = true;

  @override
  void initState() {
    super.initState();

    // Pulse animation to attract attention
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat(reverse: true);

    // ADDED: Initialize price display
    _initializePriceDisplay();
  }

  /// ADDED: Initialize price display
  Future<void> _initializePriceDisplay() async {
    try {
      // Ensure purchase service is initialized
      if (!widget.purchaseService.isInitialized) {
        await widget.purchaseService.initialize();
      }

      if (mounted) {
        setState(() {
          _displayPrice = widget.purchaseService.displayPrice;
          _priceLoading = !widget.purchaseService.priceLoaded;
        });

        if (kDebugMode) {
          print('💰 PurchaseButton: Price initialized to $_displayPrice (loading: $_priceLoading)');
        }

        // Listen for price updates
        widget.purchaseService.priceStream.listen((newPrice) {
          if (mounted) {
            setState(() {
              _displayPrice = newPrice;
              _priceLoading = false;
            });
            if (kDebugMode) {
              print('💰 PurchaseButton: Price updated to $newPrice');
            }
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ PurchaseButton: Error initializing price: $e');
      }
      if (mounted) {
        setState(() {
          _displayPrice = AppConstants.FALLBACK_PRICE;
          _priceLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: widget.purchaseService.purchaseStream,
      initialData: widget.purchaseService.isPurchased,
      builder: (context, snapshot) {
        final isPurchased = snapshot.data ?? false;

        if (isPurchased) {
          return _buildPurchasedWidget();
        }

        return _buildPurchaseWidget();
      },
    );
  }

  /// Widget for displaying "purchased" state
  Widget _buildPurchasedWidget() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade400, Colors.green.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              color: Colors.green.shade600,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'All levels unlocked!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Thank you for your purchase',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Widget for purchase with real price display
  Widget _buildPurchaseWidget() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  widget.backgroundColor ?? Colors.blue.shade400,
                  widget.backgroundColor?.withValues(alpha: 0.8) ?? Colors.blue.shade600,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                BoxShadow(
                  color: (widget.backgroundColor ?? Colors.blue).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.lock_open,
                        color: widget.textColor ?? Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Unlock all levels',
                            style: TextStyle(
                              color: widget.textColor ?? Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Access to all ${AppConstants.TOTAL_GAMES_COUNT} games',
                            style: TextStyle(
                              color: (widget.textColor ?? Colors.white).withValues(alpha: 0.8),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // ENHANCED: Purchase button with real price and loading state
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: (_isProcessing || _priceLoading) ? null : _handlePurchaseButtonPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: widget.backgroundColor ?? Colors.blue,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: _buildButtonContent(),
                  ),
                ),

                if (widget.showRestoreButton) ...[
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: (_isProcessing || _priceLoading) ? null : _handleRestorePurchases,
                    child: Text(
                      'Restore Purchases',
                      style: TextStyle(
                        color: (widget.textColor ?? Colors.white).withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// ADDED: Build button content with price and loading states
  Widget _buildButtonContent() {
    if (_isProcessing) {
      return const SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    if (_priceLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 16,
            width: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Loading price...',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      );
    }

    // Show real price with fallback indicator
    final isRealPrice = _displayPrice != AppConstants.FALLBACK_PRICE;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'For Parents: $_displayPrice',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (!isRealPrice) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.info_outline,
                size: 14,
                color: Colors.grey.shade600,
              ),
            ],
          ],
        ),
        if (!isRealPrice) ...[
          const SizedBox(height: 2),
          Text(
            'Fallback price - actual price shown by store',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  /// Handle purchase button press
  Future<void> _handlePurchaseButtonPressed() async {
    if (_isProcessing || _priceLoading) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Show Parent Gate
      final parentVerified = await widget.purchaseService.showParentGate(context);

      if (!parentVerified) {
        _showMessage('Verification failed', isError: false);
        return;
      }

      // Show purchase confirmation with real price
      final confirmed = await _showPurchaseConfirmation();

      if (!confirmed) {
        _showMessage('Purchase cancelled', isError: false);
        return;
      }

      // Start purchase
      await widget.purchaseService.purchaseFullGame();

    } catch (e) {
      _showMessage('Purchase error', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// Handle restore purchases (no age verification needed)
  Future<void> _handleRestorePurchases() async {
    if (_isProcessing || _priceLoading) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Restore purchases directly without parent gate
      await widget.purchaseService.restorePurchases();

      // Give time for processing
      await Future.delayed(const Duration(seconds: 2));

      if (!widget.purchaseService.isPurchased) {
        _showMessage('No purchases found', isError: false);
      } else {
        _showMessage('Purchases restored!', isError: false);
      }

    } catch (e) {
      _showMessage('Restore error', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// ENHANCED: Show purchase confirmation with real price
  Future<bool> _showPurchaseConfirmation() async {
    bool confirmed = false;

    final productDetails = widget.purchaseService.productDetails;
    final isRealPrice = _displayPrice != AppConstants.FALLBACK_PRICE;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.shopping_cart, color: Colors.blue.shade600),
            const SizedBox(width: 8),
            const Text('Purchase Confirmation'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You are about to unlock all game levels for $_displayPrice.',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),

            // ADDED: Product details if available
            if (productDetails != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productDetails.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (productDetails.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        productDetails.description,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text(
                      'Price: ${productDetails.price} (${productDetails.currencyCode})',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✓ Access to all ${AppConstants.TOTAL_GAMES_COUNT} games',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const Text(
                    '✓ One-time purchase',
                    style: TextStyle(fontSize: 14),
                  ),
                  const Text(
                    '✓ Works on this device',
                    style: TextStyle(fontSize: 14),
                  ),
                  const Text(
                    '✓ Can be restored',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // ADDED: Price accuracy disclaimer for fallback prices
            if (!isRealPrice) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'Final price will be shown by your app store',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            Text(
              'This purchase will remain available on this device.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              confirmed = true;
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
            child: Text('Confirm Purchase - $_displayPrice'),
          ),
        ],
      ),
    );

    return confirmed;
  }

  /// Show message to user
  void _showMessage(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.info,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isError ? Colors.red : Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// Simple version of purchase button for quick use with real price
class SimplePurchaseButton extends StatelessWidget {
  final PurchaseService purchaseService;

  const SimplePurchaseButton({
    super.key,
    required this.purchaseService,
  });

  @override
  Widget build(BuildContext context) {
    return PurchaseButton(
      purchaseService: purchaseService,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }
}

/// Compact purchase button that shows just the price
class CompactPurchaseButton extends StatefulWidget {
  final PurchaseService purchaseService;
  final VoidCallback? onPressed;

  const CompactPurchaseButton({
    super.key,
    required this.purchaseService,
    this.onPressed,
  });

  @override
  State<CompactPurchaseButton> createState() => _CompactPurchaseButtonState();
}

class _CompactPurchaseButtonState extends State<CompactPurchaseButton> {
  String _displayPrice = AppConstants.FALLBACK_PRICE;
  bool _priceLoading = true;

  @override
  void initState() {
    super.initState();
    _initializePriceDisplay();
  }

  Future<void> _initializePriceDisplay() async {
    try {
      if (!widget.purchaseService.isInitialized) {
        await widget.purchaseService.initialize();
      }

      if (mounted) {
        setState(() {
          _displayPrice = widget.purchaseService.displayPrice;
          _priceLoading = !widget.purchaseService.priceLoaded;
        });

        widget.purchaseService.priceStream.listen((newPrice) {
          if (mounted) {
            setState(() {
              _displayPrice = newPrice;
              _priceLoading = false;
            });
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _displayPrice = AppConstants.FALLBACK_PRICE;
          _priceLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: widget.purchaseService.purchaseStream,
      initialData: widget.purchaseService.isPurchased,
      builder: (context, snapshot) {
        final isPurchased = snapshot.data ?? false;

        if (isPurchased) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check, color: Colors.white, size: 16),
                SizedBox(width: 4),
                Text(
                  'Purchased',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        }

        return GestureDetector(
          onTap: _priceLoading ? null : widget.onPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _priceLoading
                    ? [Colors.grey.shade400, Colors.grey.shade600]
                    : [Colors.orange.shade400, Colors.orange.shade600],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: (_priceLoading ? Colors.grey : Colors.orange).withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_priceLoading) ...[
                  const SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 6),
                  const Text(
                    'Loading...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ] else ...[
                  const Icon(Icons.shopping_cart, color: Colors.white, size: 14),
                  const SizedBox(width: 4),
                  Text(
                    _displayPrice,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
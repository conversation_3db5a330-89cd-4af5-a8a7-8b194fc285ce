// lib/constants/revenue_cat_constants.dart
import 'dart:io';

/// RevenueCat configuration constants
/// This file contains all RevenueCat-specific configuration
class RevenueCatConstants {
  // RevenueCat App Configuration
  static const String REVENUECAT_APP_ID = 'app1b03eed64e';

  // RevenueCat API Keys
  // TODO: Replace with your actual RevenueCat API keys from https://app.revenuecat.com
  // EXAMPLE: static const String IOS_API_KEY = 'appl_abcdef1234567890';
  // EXAMPLE: static const String ANDROID_API_KEY = 'goog_abcdef1234567890';
  static const String IOS_API_KEY = 'appl_wMgNQbPmjzGzyIuMYIVpZESgrRW';
  static const String ANDROID_API_KEY = 'goog_YOUR_ANDROID_API_KEY_HERE'; // TODO: Add Android key when available
  
  /// Get platform-specific API key
  static String get API_KEY {
    if (Platform.isIOS) {
      return IOS_API_KEY;
    } else if (Platform.isAndroid) {
      return ANDROID_API_KEY;
    } else {
      throw UnsupportedError('Platform not supported for RevenueCat');
    }
  }
  
  // Entitlements
  // These are configured in RevenueCat dashboard under "Entitlements"
  static const String FULL_GAME_ENTITLEMENT = 'full_game_access'; // Main entitlement for unlocking all games
  static const String PREMIUM_ENTITLEMENT = 'premium_features';
  
  // Product Identifiers
  // These must match your App Store Connect / Google Play Console product IDs
  static const String IOS_FULL_GAME_PRODUCT = 'full_game_unlock_apple'; // Your actual iOS product ID
  static const String ANDROID_FULL_GAME_PRODUCT = 'full_game_unlock';
  
  /// Get platform-specific product ID
  static String get FULL_GAME_PRODUCT_ID {
    if (Platform.isIOS) {
      return IOS_FULL_GAME_PRODUCT;
    } else {
      return ANDROID_FULL_GAME_PRODUCT;
    }
  }
  
  // Offering Identifiers
  // These are configured in RevenueCat dashboard under "Offerings"
  static const String DEFAULT_OFFERING = 'default';
  static const String FULL_GAME_OFFERING = 'default'; // Using your default offering

  // Package Identifiers
  // These are configured in RevenueCat dashboard under each offering
  static const String FULL_GAME_PACKAGE = 'Lifetime'; // Your actual package name
  static const String MONTHLY_PACKAGE = '\$rc_monthly';
  static const String ANNUAL_PACKAGE = '\$rc_annual';
  static const String LIFETIME_PACKAGE = '\$rc_lifetime';
  
  // User Attributes
  // These can be used for analytics and targeting
  static const String USER_ATTRIBUTE_GAMES_PLAYED = 'games_played';
  static const String USER_ATTRIBUTE_LAST_GAME_DATE = 'last_game_date';
  static const String USER_ATTRIBUTE_FAVORITE_GAME = 'favorite_game';
  static const String USER_ATTRIBUTE_TOTAL_SCORE = 'total_score';
  
  // RevenueCat Dashboard URLs for reference
  static const String DASHBOARD_URL = 'https://app.revenuecat.com';
  static const String ENTITLEMENTS_URL = 'https://app.revenuecat.com/entitlements';
  static const String PRODUCTS_URL = 'https://app.revenuecat.com/products';
  static const String OFFERINGS_URL = 'https://app.revenuecat.com/offerings';
  
  // Configuration validation
  static bool get isConfigured {
    // Check if we have valid API keys for the current platform
    if (Platform.isIOS) {
      return IOS_API_KEY != 'appl_YOUR_IOS_API_KEY_HERE' && IOS_API_KEY.isNotEmpty;
    } else if (Platform.isAndroid) {
      return ANDROID_API_KEY != 'goog_YOUR_ANDROID_API_KEY_HERE' && ANDROID_API_KEY.isNotEmpty;
    }
    return false; // Unsupported platform
  }
  
  static String get configurationStatus {
    if (!isConfigured) {
      return 'RevenueCat API keys not configured. Please update RevenueCatConstants.';
    }
    return 'RevenueCat configuration is valid.';
  }
}

/// RevenueCat setup instructions
class RevenueCatSetupInstructions {
  static const String SETUP_GUIDE = '''
RevenueCat Setup Instructions:

1. Create RevenueCat Account:
   - Go to https://app.revenuecat.com
   - Sign up for a free account
   - Create a new project for "Brainy Bunny"

2. Configure API Keys:
   - In RevenueCat dashboard, go to Project Settings → API Keys
   - Copy the iOS API key and replace IOS_API_KEY in RevenueCatConstants
   - Copy the Android API key and replace ANDROID_API_KEY in RevenueCatConstants

3. Set up Products:
   - Go to Products section in RevenueCat dashboard
   - Add product with ID: full_game_unlock_apple (iOS)
   - Add product with ID: full_game_unlock (Android)
   - These must match your App Store Connect / Google Play Console products

4. Configure Entitlements:
   - Go to Entitlements section
   - Create entitlement: "full_game_access"
   - Attach the products to this entitlement

5. Set up Offerings:
   - Go to Offerings section
   - Create offering: "full_game_unlock"
   - Add package: "full_game_package" with your product
   - Set as current offering

6. Test Integration:
   - Use RevenueCat's sandbox environment for testing
   - Test purchases with sandbox accounts
   - Verify entitlements are granted correctly

For detailed setup guide, visit:
https://docs.revenuecat.com/docs/getting-started
''';
}

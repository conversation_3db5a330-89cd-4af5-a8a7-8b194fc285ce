// lib/services/revenue_cat_service.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/constants/revenue_cat_constants.dart';
import 'package:brainy_bunny/services/adjust_service.dart';

/// RevenueCat service for managing in-app purchases and subscriptions
/// Replaces the legacy PurchaseManager with modern RevenueCat SDK
class RevenueCatService {
  // Singleton pattern
  static final RevenueCatService _instance = RevenueCatService._internal();
  static RevenueCatService get instance => _instance;
  RevenueCatService._internal();

  // Product and Entitlement Configuration
  static String get _entitlementId => RevenueCatConstants.FULL_GAME_ENTITLEMENT;
  static String get _productId => RevenueCatConstants.FULL_GAME_PRODUCT_ID;
  
  // State management
  bool _isInitialized = false;
  bool _isPurchased = false;
  CustomerInfo? _customerInfo;
  Offerings? _offerings;
  Package? _currentPackage;
  
  // Stream controllers for reactive UI updates
  final _purchaseStateController = StreamController<bool>.broadcast();
  final _customerInfoController = StreamController<CustomerInfo?>.broadcast();
  final _offeringsController = StreamController<Offerings?>.broadcast();
  
  // Streams for UI to listen to
  Stream<bool> get purchaseStream => _purchaseStateController.stream;
  Stream<CustomerInfo?> get customerInfoStream => _customerInfoController.stream;
  Stream<Offerings?> get offeringsStream => _offeringsController.stream;
  
  // Callback functions for UI compatibility with existing PurchaseManager
  VoidCallback? onPurchaseSuccess;
  VoidCallback? onPurchaseError;
  VoidCallback? onPurchasePending;
  VoidCallback? onPurchaseInvalid;
  VoidCallback? onPurchasePendingRemove;
  
  // Getters for compatibility with existing code
  bool get isInitialized => _isInitialized;
  bool get isPurchased => _isPurchased;
  bool get isFullGamePurchased => _isPurchased;
  bool get priceLoaded => _currentPackage != null;
  String? get priceLoadError => null; // RevenueCat handles errors differently
  
  String get displayPrice {
    if (_currentPackage != null) {
      return _currentPackage!.storeProduct.priceString;
    }
    return AppConstants.FALLBACK_PRICE;
  }
  
  bool get isUsingFallbackPrice => _currentPackage == null;
  
  /// Initialize RevenueCat SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('🔧 RevenueCat already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === REVENUECAT INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   Debug mode: $kDebugMode');
        print('   Bundle ID: com.goodkarmalab.brainyBunny');
      }

      // Check configuration
      if (!RevenueCatConstants.isConfigured) {
        throw Exception('RevenueCat not configured: ${RevenueCatConstants.configurationStatus}');
      }

      // Configure RevenueCat
      final apiKey = RevenueCatConstants.API_KEY;
      PurchasesConfiguration configuration = PurchasesConfiguration(apiKey);

      if (kDebugMode) {
        print('   Using API Key: ${apiKey.substring(0, 10)}...');
        print('   Product ID: $_productId');
        print('   Entitlement ID: $_entitlementId');
      }

      // Set user ID if available (optional)
      // configuration.appUserID = 'your_user_id_here';
      
      // Enable debug logs in debug mode
      if (kDebugMode) {
        await Purchases.setLogLevel(LogLevel.debug);
      }

      // Initialize RevenueCat
      await Purchases.configure(configuration);

      // Set up purchase listener
      Purchases.addCustomerInfoUpdateListener(_onCustomerInfoUpdate);

      // Load initial data
      await _loadCustomerInfo();
      await _loadOfferings();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ RevenueCat initialized successfully');
        print('   Customer ID: ${await Purchases.appUserID}');
        print('   Is purchased: $_isPurchased');
        print('   Current package: ${_currentPackage?.identifier}');
        print('   Price: ${displayPrice}');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ RevenueCat initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Load customer info and update purchase state
  Future<void> _loadCustomerInfo() async {
    try {
      _customerInfo = await Purchases.getCustomerInfo();
      _updatePurchaseState();
      _customerInfoController.add(_customerInfo);
      
      if (kDebugMode) {
        print('📊 Customer info loaded:');
        print('   Active entitlements: ${_customerInfo?.entitlements.active.keys}');
        print('   All purchases: ${_customerInfo?.allPurchasedProductIdentifiers}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load customer info: $e');
      }
    }
  }

  /// Load available offerings
  Future<void> _loadOfferings() async {
    try {
      _offerings = await Purchases.getOfferings();
      
      // Find our product package
      if (_offerings?.current != null) {
        for (Package package in _offerings!.current!.availablePackages) {
          if (package.storeProduct.identifier == _productId) {
            _currentPackage = package;
            break;
          }
        }
      }
      
      _offeringsController.add(_offerings);
      
      if (kDebugMode) {
        print('📦 Offerings loaded:');
        print('   Current offering: ${_offerings?.current?.identifier}');
        print('   Available packages: ${_offerings?.current?.availablePackages.length}');
        print('   Found our package: ${_currentPackage != null}');
        if (_currentPackage != null) {
          print('   Package price: ${_currentPackage!.storeProduct.priceString}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load offerings: $e');
      }
    }
  }

  /// Handle customer info updates
  void _onCustomerInfoUpdate(CustomerInfo customerInfo) {
    _customerInfo = customerInfo;
    _updatePurchaseState();
    _customerInfoController.add(customerInfo);
    
    if (kDebugMode) {
      print('🔄 Customer info updated:');
      print('   Active entitlements: ${customerInfo.entitlements.active.keys}');
    }
  }

  /// Update purchase state based on customer info
  void _updatePurchaseState() {
    final wasPurchased = _isPurchased;
    _isPurchased = _customerInfo?.entitlements.active[_entitlementId]?.isActive ?? false;
    
    if (wasPurchased != _isPurchased) {
      _purchaseStateController.add(_isPurchased);
      
      if (kDebugMode) {
        print('🔄 Purchase state changed: $wasPurchased → $_isPurchased');
      }
    }
  }

  /// Purchase the full game unlock
  Future<void> purchaseFullGame() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ RevenueCat not initialized. Cannot purchase.');
      }
      onPurchaseError?.call();
      return;
    }

    if (_currentPackage == null) {
      if (kDebugMode) {
        print('⚠️ No package available for purchase.');
      }
      onPurchaseError?.call();
      return;
    }

    try {
      if (kDebugMode) {
        print('💰 Starting purchase for package: ${_currentPackage!.identifier}');
        print('   Product: ${_currentPackage!.storeProduct.identifier}');
        print('   Price: ${_currentPackage!.storeProduct.priceString}');
      }

      onPurchasePending?.call();

      final purchaseResult = await Purchases.purchasePackage(_currentPackage!);
      
      // Track purchase with Adjust (enhanced tracking)
      if (AdjustService.instance.isInitialized) {
        final transactionId = DateTime.now().toIso8601String();

        // Track the purchase event
        AdjustService.instance.trackPurchase(
          _currentPackage!.storeProduct.identifier,
          _currentPackage!.storeProduct.price,
          _currentPackage!.storeProduct.currencyCode ?? 'USD',
          transactionId,
        );

        // Track additional RevenueCat-specific events
        AdjustService.instance.trackEvent(
          'revenuecat_purchase_success',
          callbackParameters: {
            'product_id': _currentPackage!.storeProduct.identifier,
            'package_id': _currentPackage!.identifier,
            'offering_id': _offerings?.current?.identifier ?? 'unknown',
            'customer_id': await Purchases.appUserID,
            'entitlement_id': _entitlementId,
          },
          partnerParameters: {
            'rc_product_id': _currentPackage!.storeProduct.identifier,
            'rc_customer_id': await Purchases.appUserID,
          },
          revenue: _currentPackage!.storeProduct.price,
          currency: _currentPackage!.storeProduct.currencyCode ?? 'USD',
          transactionId: transactionId,
        );

        if (kDebugMode) {
          print('📊 Adjust: RevenueCat purchase tracked');
          print('   Product: ${_currentPackage!.storeProduct.identifier}');
          print('   Price: ${_currentPackage!.storeProduct.price}');
          print('   Currency: ${_currentPackage!.storeProduct.currencyCode}');
          print('   Transaction ID: $transactionId');
        }
      }

      _customerInfo = purchaseResult.customerInfo;
      _updatePurchaseState();

      if (_isPurchased) {
        if (kDebugMode) {
          print('✅ Purchase successful!');
        }
        onPurchasePendingRemove?.call();
        onPurchaseSuccess?.call();
      } else {
        if (kDebugMode) {
          print('⚠️ Purchase completed but entitlement not active');
        }
        onPurchasePendingRemove?.call();
        onPurchaseError?.call();
      }

    } catch (e) {
      onPurchasePendingRemove?.call();
      
      if (kDebugMode) {
        print('❌ Purchase failed with error: $e');
      }

      // For now, just call error callback
      onPurchaseError?.call();
    } catch (e) {
      onPurchasePendingRemove?.call();
      if (kDebugMode) {
        print('❌ Unexpected purchase error: $e');
      }
      onPurchaseError?.call();
    }
  }

  /// Restore previous purchases
  Future<void> restorePurchases() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ RevenueCat not initialized. Cannot restore purchases.');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🔄 Restoring purchases...');
      }

      final customerInfo = await Purchases.restorePurchases();
      _customerInfo = customerInfo;
      _updatePurchaseState();

      if (kDebugMode) {
        print('✅ Purchases restored successfully');
        print('   Active entitlements: ${customerInfo.entitlements.active.keys}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to restore purchases: $e');
      }
    }
  }

  /// Get customer info
  CustomerInfo? get customerInfo => _customerInfo;

  /// Get current offerings
  Offerings? get offerings => _offerings;

  /// Get current package for purchase
  Package? get currentPackage => _currentPackage;

  /// Check if user has active entitlement
  bool hasEntitlement(String entitlementId) {
    return _customerInfo?.entitlements.active[entitlementId]?.isActive ?? false;
  }

  /// Dispose resources
  void dispose() {
    _purchaseStateController.close();
    _customerInfoController.close();
    _offeringsController.close();
  }
}

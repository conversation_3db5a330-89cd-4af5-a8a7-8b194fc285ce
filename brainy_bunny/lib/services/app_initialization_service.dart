// lib/services/app_initialization_service.dart
import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/firebase_options.dart';
import 'package:brainy_bunny/services/auth_service.dart';
import 'package:brainy_bunny/services/purchase_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';

/// Service to handle all app initialization in the background
/// This prevents blocking the main thread and showing white screen
class AppInitializationService {
  static final AppInitializationService _instance = AppInitializationService._internal();
  static AppInitializationService get instance => _instance;
  
  AppInitializationService._internal();

  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _initializationError;
  
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String? get initializationError => _initializationError;

  /// Initialize all app services in the background
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    if (_isInitializing) {
      // Wait for ongoing initialization
      while (_isInitializing && !_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }

    _isInitializing = true;
    _initializationError = null;

    try {
      if (kDebugMode) {
        print('🚀 Starting background app initialization...');
      }

      // Step 1: Initialize Firebase
      await _initializeFirebase();

      // Step 2: Initialize Adjust SDK
      await _initializeAdjustSDK();

      // Step 3: Initialize AuthService
      await _initializeAuthService();

      // Step 4: Initialize PurchaseService (RevenueCat)
      await _initializePurchaseService();

      _isInitialized = true;
      _isInitializing = false;

      if (kDebugMode) {
        print('✅ All services initialized successfully');
      }

      return true;

    } catch (e, stackTrace) {
      _initializationError = e.toString();
      _isInitializing = false;
      
      if (kDebugMode) {
        print('❌ App initialization failed: $e');
        print('Stack trace: $stackTrace');
      }

      return false;
    }
  }

  /// Initialize Firebase
  Future<void> _initializeFirebase() async {
    try {
      if (kDebugMode) {
        print('🔥 Initializing Firebase...');
      }

      await Firebase.initializeApp(
        name: 'brainy_bunny',
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Set named app as default instance for convenience
      Firebase.app('brainy_bunny').setAutomaticDataCollectionEnabled(true);

      if (kDebugMode) {
        print('✅ Firebase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize Adjust SDK
  Future<void> _initializeAdjustSDK() async {
    try {
      if (kDebugMode) {
        print('📊 Initializing Adjust SDK...');
      }

      await AdjustService.instance.initialize();

      if (kDebugMode) {
        print('✅ Adjust SDK initialized successfully');
        print('   - App Token: ${AdjustService.instance.appToken}');
        print('   - Environment: ${kDebugMode ? "Sandbox" : "Production"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Adjust SDK initialization failed: $e');
        print('   App will continue without Adjust tracking');
      }
      // Don't rethrow - Adjust is not critical for app functionality
      // The app should continue to work even if Adjust fails to initialize
    }
  }

  /// Initialize PurchaseService (RevenueCat)
  Future<void> _initializePurchaseService() async {
    try {
      if (kDebugMode) {
        print('💰 Initializing PurchaseService...');
      }

      await PurchaseService.instance.initialize();

      if (kDebugMode) {
        print('✅ PurchaseService initialized successfully');
        print('   Is purchased: ${PurchaseService.instance.isPurchased}');
        print('   Price: ${PurchaseService.instance.displayPrice}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ PurchaseService initialization failed: $e');
        print('   This may affect in-app purchase functionality');
      }
      // Don't rethrow - app should continue even if purchases fail
    }
  }

  /// Initialize AuthService
  Future<void> _initializeAuthService() async {
    try {
      if (kDebugMode) {
        print('🔐 Initializing AuthService...');
      }

      await AuthService.instance.initialize();

      if (kDebugMode) {
        print('✅ AuthService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AuthService initialization failed: $e');
      }
      rethrow;
    }
  }



  /// Reset initialization state (for testing or retry scenarios)
  void reset() {
    _isInitialized = false;
    _isInitializing = false;
    _initializationError = null;
  }
}

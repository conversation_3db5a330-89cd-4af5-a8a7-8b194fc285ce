// lib/services/paywall_manager.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/services/purchase_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';
import 'package:brainy_bunny/ui/overlays/revenuecat_paywall.dart';

/// Manages when and how to show the RevenueCat paywall
/// Handles trial completion and locked level access
class PaywallManager {
  static final PaywallManager _instance = PaywallManager._internal();
  factory PaywallManager() => _instance;
  PaywallManager._internal();
  
  static PaywallManager get instance => _instance;
  
  bool _hasShownTrialCompletePaywall = false;
  int _lockedLevelAttempts = 0;
  
  /// Check if user should see paywall when trying to access a level
  bool shouldShowPaywallForLevel(int levelIndex) {
    // If already purchased, never show paywall
    if (PurchaseService.instance.isPurchased) {
      return false;
    }
    
    // If it's a free level (trial), don't show paywall
    if (levelIndex < AppConstants.FREE_GAMES_COUNT) {
      return false;
    }
    
    // User is trying to access a locked level
    return true;
  }
  
  /// Check if user has completed the trial and should see completion paywall
  bool shouldShowTrialCompletePaywall(int completedLevelIndex) {
    // If already purchased, never show paywall
    if (PurchaseService.instance.isPurchased) {
      return false;
    }
    
    // If we've already shown the trial complete paywall, don't show again
    if (_hasShownTrialCompletePaywall) {
      return false;
    }
    
    // If user just completed the last free level, show trial complete paywall
    if (completedLevelIndex == AppConstants.FREE_GAMES_COUNT - 1) {
      _hasShownTrialCompletePaywall = true;
      return true;
    }
    
    return false;
  }
  
  /// Show paywall when user tries to access locked level
  Future<bool> showLockedLevelPaywall(BuildContext context, int levelIndex) async {
    _lockedLevelAttempts++;
    
    if (kDebugMode) {
      print('🔒 Showing locked level paywall for level $levelIndex (attempt $_lockedLevelAttempts)');
    }
    
    // Track the locked level attempt
    AdjustService.instance.trackEvent('locked_level_attempted',
      callbackParameters: {
        'level_index': levelIndex.toString(),
        'attempt_count': _lockedLevelAttempts.toString(),
        'free_levels_completed': AppConstants.FREE_GAMES_COUNT.toString(),
      },
    );
    
    return await _showPaywall(
      context,
      triggeredFrom: 'locked_level',
      levelIndex: levelIndex,
    );
  }
  
  /// Show paywall when user completes trial period
  Future<bool> showTrialCompletePaywall(BuildContext context) async {
    if (kDebugMode) {
      print('🎉 Showing trial complete paywall - user finished free levels');
    }
    
    // Track trial completion
    AdjustService.instance.trackEvent('trial_completed',
      callbackParameters: {
        'free_levels_count': AppConstants.FREE_GAMES_COUNT.toString(),
        'total_levels_count': AppConstants.TOTAL_GAMES_COUNT.toString(),
      },
    );
    
    return await _showPaywall(
      context,
      triggeredFrom: 'trial_complete',
    );
  }
  
  /// Show paywall manually (e.g., from settings or upgrade button)
  Future<bool> showManualPaywall(BuildContext context) async {
    if (kDebugMode) {
      print('⚙️ Showing manual paywall - user requested upgrade');
    }
    
    return await _showPaywall(
      context,
      triggeredFrom: 'manual',
    );
  }
  
  /// Internal method to show the paywall
  Future<bool> _showPaywall(
    BuildContext context, {
    required String triggeredFrom,
    int? levelIndex,
  }) async {
    bool purchaseSuccessful = false;
    
    try {
      await showDialog<void>(
        context: context,
        barrierDismissible: false, // User must interact with paywall
        builder: (BuildContext context) {
          return RevenueCatPaywall(
            triggeredFrom: triggeredFrom,
            onPurchaseSuccess: () {
              purchaseSuccessful = true;
              if (kDebugMode) {
                print('✅ Paywall purchase successful!');
              }
            },
            onPurchaseError: () {
              if (kDebugMode) {
                print('❌ Paywall purchase failed');
              }
            },
            onClose: () {
              if (kDebugMode) {
                print('🚪 Paywall closed by user');
              }
            },
          );
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error showing paywall: $e');
      }
    }
    
    // Track paywall result
    AdjustService.instance.trackEvent('paywall_result',
      callbackParameters: {
        'trigger': triggeredFrom,
        'success': purchaseSuccessful.toString(),
        'level_index': levelIndex?.toString() ?? 'null',
      },
    );
    
    return purchaseSuccessful;
  }
  
  /// Reset paywall state (useful for testing)
  void resetPaywallState() {
    _hasShownTrialCompletePaywall = false;
    _lockedLevelAttempts = 0;
    
    if (kDebugMode) {
      print('🔄 Paywall state reset');
    }
  }
  
  /// Get trial progress information
  Map<String, dynamic> getTrialProgress() {
    return {
      'free_levels_count': AppConstants.FREE_GAMES_COUNT,
      'total_levels_count': AppConstants.TOTAL_GAMES_COUNT,
      'locked_levels_count': AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT,
      'has_shown_trial_complete': _hasShownTrialCompletePaywall,
      'locked_level_attempts': _lockedLevelAttempts,
      'is_purchased': PurchaseService.instance.isPurchased,
    };
  }
  
  /// Check if user is in trial period
  bool get isInTrialPeriod {
    return !PurchaseService.instance.isPurchased;
  }
  
  /// Get remaining free levels
  int get remainingFreeLevels {
    if (PurchaseService.instance.isPurchased) {
      return 0; // No limits for purchased users
    }
    
    // This would need to be tracked based on actual user progress
    // For now, we assume all free levels are available
    return AppConstants.FREE_GAMES_COUNT;
  }
  
  /// Get locked levels count
  int get lockedLevelsCount {
    if (PurchaseService.instance.isPurchased) {
      return 0; // No locked levels for purchased users
    }
    
    return AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT;
  }
}

// lib/services/purchase_service.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/services/revenue_cat_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';
import 'package:brainy_bunny/constants/revenue_cat_constants.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Modern purchase service using RevenueCat SDK
/// Replaces the legacy PurchaseManager completely
class PurchaseService {
  // Singleton pattern
  static final PurchaseService _instance = PurchaseService._internal();
  static PurchaseService get instance => _instance;
  PurchaseService._internal();
  
  // State
  bool _isInitialized = false;

  // Stream controllers for purchase state
  final _purchaseStateController = StreamController<bool>.broadcast();
  final _priceController = StreamController<String>.broadcast();

  // Streams
  Stream<bool> get purchaseStream => _purchaseStateController.stream;
  Stream<String> get priceStream => _priceController.stream;

  // Callback functions (compatible interface)
  VoidCallback? onPurchaseSuccess;
  VoidCallback? onPurchaseError;
  VoidCallback? onPurchasePending;
  VoidCallback? onPurchaseInvalid;
  VoidCallback? onPurchasePendingRemove;

  // Getters - delegate to RevenueCat service
  bool get isInitialized => _isInitialized && RevenueCatService.instance.isInitialized;
  bool get isPurchased => RevenueCatService.instance.isPurchased;
  bool get isFullGamePurchased => isPurchased;
  bool get priceLoaded => RevenueCatService.instance.priceLoaded;
  String? get priceLoadError => RevenueCatService.instance.priceLoadError;
  String get displayPrice => RevenueCatService.instance.displayPrice;
  bool get isUsingFallbackPrice => RevenueCatService.instance.isUsingFallbackPrice;
  
  /// Initialize the purchase service
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('🔧 PurchaseService already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === PURCHASE SERVICE INITIALIZATION ===');
        print('   RevenueCat configured: ${RevenueCatConstants.isConfigured}');
      }

      // Check if RevenueCat is configured
      if (!RevenueCatConstants.isConfigured) {
        if (kDebugMode) {
          print('⚠️ RevenueCat not configured for current platform');
          if (Platform.isIOS) {
            print('   iOS API key status: ${RevenueCatConstants.IOS_API_KEY != 'appl_YOUR_IOS_API_KEY_HERE' ? 'configured' : 'missing'}');
          } else if (Platform.isAndroid) {
            print('   Android API key status: ${RevenueCatConstants.ANDROID_API_KEY != 'goog_YOUR_ANDROID_API_KEY_HERE' ? 'configured' : 'missing'}');
          }
        }
        throw Exception('RevenueCat not configured for ${Platform.operatingSystem}. Please update API keys in RevenueCatConstants.');
      }

      // Initialize RevenueCat
      await RevenueCatService.instance.initialize();

      if (kDebugMode) {
        print('✅ RevenueCat initialized successfully');
        print('   RevenueCat status: ${RevenueCatService.instance.isPurchased}');
        print('   Display price: ${RevenueCatService.instance.displayPrice}');
      }

      // Setup RevenueCat callbacks
      _setupRevenueCatCallbacks();

      // Setup stream listeners
      _setupStreamListeners();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ PurchaseService initialized successfully');
        print('   Purchase status: $isPurchased');
        print('   Display price: $displayPrice');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ PurchaseService initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Setup RevenueCat callbacks
  void _setupRevenueCatCallbacks() {
    RevenueCatService.instance.onPurchaseSuccess = () {
      if (kDebugMode) {
        print('🎉 RevenueCat purchase success');
      }

      // Track purchase success with Adjust
      if (AdjustService.instance.isInitialized) {
        AdjustService.instance.trackEvent(
          'purchase_service_success',
          callbackParameters: {
            'service': 'revenuecat',
            'status': 'success',
          },
        );
      }

      _purchaseStateController.add(isPurchased);
      onPurchaseSuccess?.call();
    };

    RevenueCatService.instance.onPurchaseError = () {
      if (kDebugMode) {
        print('❌ RevenueCat purchase error');
      }
      onPurchaseError?.call();
    };

    RevenueCatService.instance.onPurchasePending = () {
      if (kDebugMode) {
        print('⏳ RevenueCat purchase pending');
      }
      onPurchasePending?.call();
    };

    RevenueCatService.instance.onPurchaseInvalid = () {
      if (kDebugMode) {
        print('⚠️ RevenueCat purchase invalid');
      }
      onPurchaseInvalid?.call();
    };

    RevenueCatService.instance.onPurchasePendingRemove = () {
      if (kDebugMode) {
        print('✅ RevenueCat purchase pending removed');
      }
      onPurchasePendingRemove?.call();
    };
  }

  /// Setup stream listeners
  void _setupStreamListeners() {
    // Listen to RevenueCat purchase updates
    RevenueCatService.instance.purchaseStream.listen((purchased) {
      _purchaseStateController.add(isPurchased);
    });
  }

  /// Purchase the full game
  Future<void> purchaseFullGame() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ PurchaseService not initialized');
      }
      onPurchaseError?.call();
      return;
    }

    if (kDebugMode) {
      print('💰 Starting purchase via RevenueCat');
    }

    try {
      await RevenueCatService.instance.purchaseFullGame();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Purchase failed: $e');
      }
      onPurchaseError?.call();
    }
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ PurchaseService not initialized');
      }
      return;
    }

    if (kDebugMode) {
      print('🔄 Restoring purchases via RevenueCat');
    }

    try {
      await RevenueCatService.instance.restorePurchases();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Restore failed: $e');
      }
    }
  }

  /// Get service status for debugging
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'revenueCatConfigured': RevenueCatConstants.isConfigured,
      'revenueCatInitialized': RevenueCatService.instance.isInitialized,
      'isPurchased': isPurchased,
      'displayPrice': displayPrice,
      'priceLoaded': priceLoaded,
    };
  }

  /// Get product details (for compatibility)
  dynamic get productDetails => null; // RevenueCat handles this differently

  /// Show parent gate (for compatibility)
  Future<bool> showParentGate(BuildContext context) async {
    // For now, just return true - implement parent gate if needed
    return true;
  }

  /// Dispose resources
  void dispose() {
    _purchaseStateController.close();
    _priceController.close();
    RevenueCatService.instance.dispose();
  }
}

// lib/services/adjust_service.dart
import 'package:adjust_sdk/adjust.dart';
import 'package:adjust_sdk/adjust_config.dart';
import 'package:adjust_sdk/adjust_event.dart';
import 'package:adjust_sdk/adjust_attribution.dart';
import 'package:adjust_sdk/adjust_event_failure.dart';
import 'package:adjust_sdk/adjust_event_success.dart';
import 'package:adjust_sdk/adjust_session_failure.dart';
import 'package:adjust_sdk/adjust_session_success.dart';
import 'package:flutter/foundation.dart';

/// Service class to handle all Adjust SDK operations
/// Provides centralized management of attribution tracking, events, and analytics
class AdjustService {
  static const String _appToken = '2k0f6660xxxc';
  static AdjustService? _instance;
  bool _isInitialized = false;

  // Private constructor for singleton pattern
  AdjustService._();

  /// Get singleton instance of AdjustService
  static AdjustService get instance {
    _instance ??= AdjustService._();
    return _instance!;
  }

  /// Initialize Adjust SDK with proper configuration
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('🔧 Adjust SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 Initializing Adjust SDK with app token: $_appToken');
      }

      // Create Adjust configuration
      AdjustConfig config = AdjustConfig(
        _appToken,
        kDebugMode ? AdjustEnvironment.sandbox : AdjustEnvironment.production,
      );

      // Set log level based on build mode
      config.logLevel = kDebugMode ? AdjustLogLevel.verbose : AdjustLogLevel.warn;

      // Set attribution callback to receive attribution data
      config.attributionCallback = (AdjustAttribution attribution) {
        if (kDebugMode) {
          print('📊 Adjust Attribution received:');
          print('  - Tracker Token: ${attribution.trackerToken}');
          print('  - Tracker Name: ${attribution.trackerName}');
          print('  - Network: ${attribution.network}');
          print('  - Campaign: ${attribution.campaign}');
          print('  - Adgroup: ${attribution.adgroup}');
          print('  - Creative: ${attribution.creative}');
          print('  - Click Label: ${attribution.clickLabel}');
          print('  - Cost Type: ${attribution.costType}');
          print('  - Cost Amount: ${attribution.costAmount}');
          print('  - Cost Currency: ${attribution.costCurrency}');
        }
        
        // Handle attribution data (e.g., store in analytics, update UI, etc.)
        _handleAttribution(attribution);
      };

      // Set session success callback
      config.sessionSuccessCallback = (AdjustSessionSuccess sessionSuccess) {
        if (kDebugMode) {
          print('✅ Adjust Session Success:');
          print('  - Message: ${sessionSuccess.message}');
          print('  - Timestamp: ${sessionSuccess.timestamp}');
          print('  - Adid: ${sessionSuccess.adid}');
        }
      };

      // Set session failure callback
      config.sessionFailureCallback = (AdjustSessionFailure sessionFailure) {
        if (kDebugMode) {
          print('❌ Adjust Session Failure:');
          print('  - Message: ${sessionFailure.message}');
          print('  - Timestamp: ${sessionFailure.timestamp}');
          print('  - Adid: ${sessionFailure.adid}');
          print('  - Will Retry: ${sessionFailure.willRetry}');
        }
      };

      // Set event success callback
      config.eventSuccessCallback = (AdjustEventSuccess eventSuccess) {
        if (kDebugMode) {
          print('✅ Adjust Event Success:');
          print('  - Message: ${eventSuccess.message}');
          print('  - Timestamp: ${eventSuccess.timestamp}');
          print('  - Adid: ${eventSuccess.adid}');
          print('  - Event Token: ${eventSuccess.eventToken}');
          print('  - Callback Id: ${eventSuccess.callbackId}');
        }
      };

      // Set event failure callback
      config.eventFailureCallback = (AdjustEventFailure eventFailure) {
        if (kDebugMode) {
          print('❌ Adjust Event Failure:');
          print('  - Message: ${eventFailure.message}');
          print('  - Timestamp: ${eventFailure.timestamp}');
          print('  - Adid: ${eventFailure.adid}');
          print('  - Event Token: ${eventFailure.eventToken}');
          print('  - Callback Id: ${eventFailure.callbackId}');
          print('  - Will Retry: ${eventFailure.willRetry}');
        }
      };

      // Initialize the SDK
      Adjust.initSdk(config);
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Adjust SDK initialized successfully');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Failed to initialize Adjust SDK: $e');
        print('Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Track a custom event with optional parameters
  void trackEvent(String eventToken, {
    Map<String, String>? callbackParameters,
    Map<String, String>? partnerParameters,
    double? revenue,
    String? currency,
    String? transactionId,
  }) {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK not initialized. Cannot track event: $eventToken');
      }
      return;
    }

    try {
      AdjustEvent event = AdjustEvent(eventToken);

      // Add callback parameters
      if (callbackParameters != null) {
        callbackParameters.forEach((key, value) {
          event.addCallbackParameter(key, value);
        });
      }

      // Add partner parameters
      if (partnerParameters != null) {
        partnerParameters.forEach((key, value) {
          event.addPartnerParameter(key, value);
        });
      }

      // Set revenue if provided
      if (revenue != null && currency != null) {
        event.setRevenue(revenue, currency);
      }

      // Set transaction ID if provided (using property assignment)
      if (transactionId != null) {
        event.transactionId = transactionId;
      }

      Adjust.trackEvent(event);

      if (kDebugMode) {
        print('📈 Tracked Adjust event: $eventToken');
        if (revenue != null) {
          print('  - Revenue: $revenue $currency');
        }
        if (callbackParameters != null) {
          print('  - Callback Parameters: $callbackParameters');
        }
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Failed to track Adjust event $eventToken: $e');
        print('Stack trace: $stackTrace');
      }
    }
  }

  /// Track app launch event
  void trackAppLaunch() {
    if (kDebugMode) {
      print('🚀 Tracking app launch event');
    }
    // You can create a specific event token in Adjust dashboard for app launches
    // For now, we'll just log the session start which is automatic
  }

  /// Track game start event
  void trackGameStart(String gameType) {
    trackEvent(
      'game_start', // You'll need to create this event token in Adjust dashboard
      callbackParameters: {
        'game_type': gameType,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track game completion event
  void trackGameComplete(String gameType, int score, int duration) {
    trackEvent(
      'game_complete', // You'll need to create this event token in Adjust dashboard
      callbackParameters: {
        'game_type': gameType,
        'score': score.toString(),
        'duration_seconds': duration.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track purchase event
  void trackPurchase(String productId, double price, String currency, String transactionId) {
    trackEvent(
      'purchase', // You'll need to create this event token in Adjust dashboard
      revenue: price,
      currency: currency,
      transactionId: transactionId,
      callbackParameters: {
        'product_id': productId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Get current attribution information
  Future<AdjustAttribution?> getAttribution() async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK not initialized. Cannot get attribution.');
      }
      return null;
    }

    try {
      return await Adjust.getAttribution();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get Adjust attribution: $e');
      }
      return null;
    }
  }

  /// Handle attribution data received from Adjust
  void _handleAttribution(AdjustAttribution attribution) {
    // Store attribution data for analytics
    // You can integrate this with your analytics service
    // or store in local storage for later use
    
    if (kDebugMode) {
      print('🔍 Processing attribution data...');
    }
    
    // Example: Store key attribution data
    // You might want to save this to SharedPreferences or send to your backend
    final attributionData = {
      'tracker_token': attribution.trackerToken,
      'tracker_name': attribution.trackerName,
      'network': attribution.network,
      'campaign': attribution.campaign,
      'adgroup': attribution.adgroup,
      'creative': attribution.creative,
      'click_label': attribution.clickLabel,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    // TODO: Implement attribution data handling logic here
    // For example:
    // - Send to analytics backend
    // - Store in local database
    // - Update user profile with attribution info
    // - Trigger specific app behavior based on attribution
  }

  /// Check if Adjust SDK is initialized
  bool get isInitialized => _isInitialized;

  /// Get the app token being used
  String get appToken => _appToken;
}
